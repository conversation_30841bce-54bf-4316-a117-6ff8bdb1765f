// 渲染进程JavaScript - 桌面应用风格
const { ipc<PERSON>enderer } = require('electron');

// 全局变量
let selectedProfiles = new Set();
let allProfiles = [];
let logMessages = [];
let headlessMode = false;
let minimizeMode = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
async function initializeApp() {
    addLogMessage('info', '正在检测Chrome配置文件...');
    
    try {
        await loadProfiles();
        addLogMessage('success', `检测完成，共发现 ${allProfiles.length} 个配置文件`);

    } catch (error) {
        addLogMessage('error', `检测配置文件失败: ${error.message}`);
    }
}

// 加载配置文件列表
async function loadProfiles() {
    try {
        // 调用Python脚本获取配置文件
        const result = await ipcRenderer.invoke('get-chrome-profiles');

        if (result.success) {
            allProfiles = result.profiles;
        } else {
            // 如果Python脚本失败，使用默认配置
            allProfiles = [
                { real_name: 'Default', display_name: '1' }
            ];
            addLogMessage('warning', '配置文件检测失败，使用默认配置');
        }

        renderProfiles();
    } catch (error) {
        // 出错时使用默认配置
        allProfiles = [
            { real_name: 'Default', display_name: '1' }
        ];
        addLogMessage('error', `配置文件检测出错: ${error.message}`);
        renderProfiles();
    }
}

// 渲染配置文件列表 - 固定12列网格布局
function renderProfiles() {
    const profileGrid = document.getElementById('profileGrid');
    profileGrid.innerHTML = '';

    // 渲染实际的配置文件
    allProfiles.forEach(profile => {
        const profileItem = document.createElement('div');
        profileItem.className = 'profile-item';
        profileItem.dataset.profile = profile.display_name;

        profileItem.innerHTML = `
            <div class="profile-number">${profile.display_name}</div>
        `;

        // 添加点击事件
        profileItem.onclick = () => toggleProfile(profile.display_name, profileItem);

        profileGrid.appendChild(profileItem);
    });

    // 计算需要多少个占位符来填满12的倍数
    const totalProfiles = allProfiles.length;
    const remainder = totalProfiles % 12;
    if (remainder !== 0) {
        const placeholdersNeeded = 12 - remainder;

        // 添加带边框但无数字的占位选择器
        for (let i = 0; i < placeholdersNeeded; i++) {
            const placeholder = document.createElement('div');
            placeholder.className = 'profile-item placeholder';
            // 占位符不添加任何内容，保持空白但有边框
            profileGrid.appendChild(placeholder);
        }
    }
}

// 切换配置文件选择状态
function toggleProfile(profileName, profileItem) {
    if (selectedProfiles.has(profileName)) {
        selectedProfiles.delete(profileName);
        profileItem.classList.remove('selected');
    } else {
        selectedProfiles.add(profileName);
        profileItem.classList.add('selected');
    }
    updateSelectionCount();
}

// 全选配置文件
function selectAll() {
    selectedProfiles.clear();
    allProfiles.forEach(profile => {
        selectedProfiles.add(profile.display_name);
    });
    updateProfileSelection();
    addLogMessage('info', '已选择所有配置文件');
}

// 取消全选
function deselectAll() {
    selectedProfiles.clear();
    updateProfileSelection();
    addLogMessage('info', '已取消选择所有配置文件');
}

// 刷新配置文件
async function refreshProfiles() {
    addLogMessage('info', '正在刷新配置文件...');
    try {
        await loadProfiles();
        addLogMessage('success', '配置文件刷新完成');
    } catch (error) {
        addLogMessage('error', `刷新配置文件失败: ${error.message}`);
    }
}

// 更新配置文件选择状态显示
function updateProfileSelection() {
    document.querySelectorAll('.profile-item').forEach(item => {
        const profileName = item.dataset.profile;

        if (selectedProfiles.has(profileName)) {
            item.classList.add('selected');
        } else {
            item.classList.remove('selected');
        }
    });
    updateSelectionCount();
}

// 更新选择计数
function updateSelectionCount() {
    const profileCountEl = document.getElementById('profileCount');
    const selectionCountEl = document.getElementById('selectionCount');

    if (profileCountEl) {
        profileCountEl.textContent = `共 ${allProfiles.length} 个`;
    }
    if (selectionCountEl) {
        selectionCountEl.textContent = `已选择: ${selectedProfiles.size}`;
    }
}

// 启动必应奖励 - 调用实际的主进程功能
async function launchBingRewards() {
    if (selectedProfiles.size === 0) {
        addLogMessage('warning', '请至少选择一个配置文件');
        return;
    }

    showLoading(true);
    addLogMessage('info', `开始启动必应奖励，共 ${selectedProfiles.size} 个配置文件`);

    try {
        const profiles = Array.from(selectedProfiles);

        // 调用主进程的必应奖励功能
        const result = await ipcRenderer.invoke('launch-bing-rewards', profiles);

        if (result.success) {
            addLogMessage('success', result.message);

            // 10秒后自动读取记录
            addLogMessage('info', '将在10秒后自动读取记录...');
            setTimeout(() => {
                readRecords();
            }, 10000);
        } else {
            addLogMessage('error', result.message);
        }

    } catch (error) {
        addLogMessage('error', `启动失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 读取记录
async function readRecords() {
    showLoading(true);
    addLogMessage('info', '正在读取签到记录...');

    try {
        // 调用Python脚本读取实际记录
        const result = await ipcRenderer.invoke('read-records');

        if (result.success && result.data) {
            displayResults(result.data);
            addLogMessage('success', result.message);

            // 显示文件信息
            if (result.data.file_name) {
                addLogMessage('info', `数据来源: ${result.data.file_name}`);
            }
        } else {
            addLogMessage('error', result.message || '读取记录失败');

            // 显示示例数据作为参考
            const exampleData = {
                today_date: new Date().toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' }),
                yesterday_date: new Date(Date.now() - ********).toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' }),
                accounts: [
                    { index: 1, account: "示例账号*************", today_days: 0, status: "无记录", status_class: "new" },
                    { index: 2, account: "示例账号*************", today_days: 0, status: "无记录", status_class: "new" }
                ]
            };

            displayResults(exampleData);
            addLogMessage('warning', '显示示例数据，请检查是否存在记录文件');
        }

    } catch (error) {
        addLogMessage('error', `读取记录失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 哔哩搜索 - 调用实际的主进程功能
async function launchBilibiliSearch() {
    if (selectedProfiles.size === 0) {
        addLogMessage('warning', '请至少选择一个配置文件');
        return;
    }

    showLoading(true);
    addLogMessage('info', `开始哔哩搜索，共 ${selectedProfiles.size} 个配置文件`);

    try {
        const profiles = Array.from(selectedProfiles);
        const result = await ipcRenderer.invoke('launch-bilibili-search', profiles);

        if (result.success) {
            addLogMessage('success', result.message);
        } else {
            addLogMessage('error', result.message);
        }
    } catch (error) {
        addLogMessage('error', `哔哩搜索失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 自动模式 - 调用实际的主进程功能
async function autoMode() {
    if (selectedProfiles.size === 0) {
        addLogMessage('warning', '请至少选择一个配置文件');
        return;
    }

    showLoading(true);
    addLogMessage('info', `开始自动模式，共 ${selectedProfiles.size} 个配置文件`);

    try {
        const profiles = Array.from(selectedProfiles);
        const result = await ipcRenderer.invoke('auto-mode', profiles);

        if (result.success) {
            addLogMessage('success', result.message);
        } else {
            addLogMessage('error', result.message);
        }
    } catch (error) {
        addLogMessage('error', `自动模式失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 清除缓存 - 调用实际的主进程功能
async function clearTodayCache() {
    showLoading(true);
    addLogMessage('info', '正在清除今日缓存...');

    try {
        const result = await ipcRenderer.invoke('clear-today-cache');

        if (result.success) {
            addLogMessage('success', result.message);
        } else {
            addLogMessage('error', result.message);
        }
    } catch (error) {
        addLogMessage('error', `清除缓存失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 切换无头模式 - 使用统一的选中效果
async function toggleHeadlessMode() {
    headlessMode = !headlessMode;
    const btn = document.getElementById('headlessBtn');

    try {
        // 调用主进程设置无头模式
        const result = await ipcRenderer.invoke('set-headless-mode', headlessMode);

        if (result.success) {
            if (headlessMode) {
                btn.classList.add('selected');
                addLogMessage('option', 'Headless Mode enabled');
            } else {
                btn.classList.remove('selected');
                addLogMessage('option', 'Headless Mode disabled');
            }
        } else {
            // 如果设置失败，恢复状态
            headlessMode = !headlessMode;
            addLogMessage('error', `设置无头模式失败: ${result.message}`);
        }
    } catch (error) {
        // 如果调用失败，恢复状态
        headlessMode = !headlessMode;
        addLogMessage('error', `设置无头模式失败: ${error.message}`);
    }
}

// 切换最小化模式 - 使用统一的选中效果
async function toggleMinimizeMode() {
    minimizeMode = !minimizeMode;
    const btn = document.getElementById('minimizeBtn');

    try {
        // 调用主进程设置最小化模式
        const result = await ipcRenderer.invoke('set-minimize-mode', minimizeMode);

        if (result.success) {
            if (minimizeMode) {
                btn.classList.add('selected');
                addLogMessage('option', 'Minimize Mode enabled');
            } else {
                btn.classList.remove('selected');
                addLogMessage('option', 'Minimize Mode disabled');
            }
        } else {
            // 如果设置失败，恢复状态
            minimizeMode = !minimizeMode;
            addLogMessage('error', `设置最小化模式失败: ${result.message}`);
        }
    } catch (error) {
        // 如果调用失败，恢复状态
        minimizeMode = !minimizeMode;
        addLogMessage('error', `设置最小化模式失败: ${error.message}`);
    }
}

// 清空日志
function clearLog() {
    const logContent = document.getElementById('logContent');
    logContent.innerHTML = '';
    logMessages = [];
    addLogMessage('info', 'Log cleared');
}

// 保存日志
function saveLog() {
    const logText = logMessages.join('\n');
    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chrome-launcher-log-${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    addLogMessage('info', 'Log saved');
}

// 关闭所有Chrome - 调用实际的主进程功能
async function closeAllChrome() {
    if (!confirm('确定要关闭所有Chrome进程吗？')) {
        return;
    }

    showLoading(true);
    addLogMessage('info', '正在关闭所有Chrome进程...');

    try {
        const result = await ipcRenderer.invoke('close-all-chrome');

        if (result.success) {
            addLogMessage('success', result.message);
        } else {
            addLogMessage('error', result.message);
        }
    } catch (error) {
        addLogMessage('error', `关闭Chrome进程失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 显示结果 - 确保结果面板正确显示
function displayResults(data) {
    const resultsPanel = document.getElementById('resultsPanel');
    const resultsContent = document.getElementById('resultsContent');

    if (!resultsPanel || !resultsContent) {
        addLogMessage('error', '结果面板元素未找到');
        return;
    }

    let tableHTML = `
        <div style="margin-bottom: 15px;">
            <strong>账号数据对比 (${data.yesterday_date} vs ${data.today_date})</strong>
        </div>
        <table class="results-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>账号</th>
                    <th>天数</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
    `;

    data.accounts.forEach(account => {
        tableHTML += `
            <tr>
                <td>${account.index}</td>
                <td style="font-family: monospace; font-size: 11px;">${account.account}</td>
                <td>${account.today_days}天</td>
                <td><span class="status-badge status-${account.status_class}">${account.status}</span></td>
            </tr>
        `;
    });

    tableHTML += `
            </tbody>
        </table>
    `;

    resultsContent.innerHTML = tableHTML;
    resultsPanel.style.display = 'block';
}

// 关闭结果面板
function closeResults() {
    document.getElementById('resultsPanel').style.display = 'none';
}

// 获取日志前缀符号
function getLogPrefix(type) {
    const prefixes = {
        info: '[INFO]',
        success: '[SUCCESS]',
        warning: '[WARNING]',
        error: '[ERROR]',
        option: '[OPTION]'
    };
    return prefixes[type] || prefixes.info;
}

// 添加日志消息
function addLogMessage(type, message) {
    const logContent = document.getElementById('logContent');

    // 创建简单的文本日志条目（无时间戳）
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `${getLogPrefix(type)} ${message}`;

    logContent.appendChild(logEntry);
    logContent.scrollTop = logContent.scrollHeight;

    // 保存到日志数组
    logMessages.push(`${getLogPrefix(type)} ${message}`);
}

// 清空日志
function clearLog() {
    document.getElementById('logContent').innerHTML = '';
    logMessages = [];
    addLogMessage('info', '日志已清空');
}

// 保存日志
async function saveLog() {
    if (logMessages.length === 0) {
        addLogMessage('warning', '没有日志可保存');
        return;
    }
    
    try {
        const result = await ipcRenderer.invoke('show-save-dialog');
        if (!result.canceled) {
            const content = logMessages.join('\n');
            const saveResult = await ipcRenderer.invoke('save-file', result.filePath, content);
            
            if (saveResult.success) {
                addLogMessage('success', `日志已保存到: ${result.filePath}`);
            } else {
                addLogMessage('error', `保存失败: ${saveResult.error}`);
            }
        }
    } catch (error) {
        addLogMessage('error', `保存日志失败: ${error.message}`);
    }
}

// 显示/隐藏加载状态 - 确保加载遮罩正确显示
function showLoading(show) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = show ? 'flex' : 'none';
    } else {
        console.warn('Loading overlay element not found');
    }
}

// 移除状态栏相关函数

// 监听来自主进程的日志消息
ipcRenderer.on('log-message', (event, message) => {
    addLogMessage('info', message);
});



// 键盘快捷键
document.addEventListener('keydown', (event) => {
    if (event.ctrlKey) {
        switch (event.key) {
            case 'a':
                event.preventDefault();
                selectAll();
                break;
            case 'd':
                event.preventDefault();
                deselectAll();
                break;
            case 's':
                event.preventDefault();
                saveLog();
                break;
            case 'l':
                event.preventDefault();
                clearLog();
                break;
        }
    }
});
