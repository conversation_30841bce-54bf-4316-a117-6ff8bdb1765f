#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取Chrome配置文件的签到记录 - 完全对应Python版本逻辑
"""

import os
import json
import sys
from datetime import datetime, timedelta
import re

# 配置
STALK_DIR = r"C:\Users\<USER>\Downloads\RTBS"

def read_folder_account_data(folder_path):
    """读取文件夹中所有txt文件的账号数据，返回字典格式 {账号: 天数}
    处理重复下载文件（忽略(1)(2)等后缀），对同一账号选择最大天数"""
    try:
        if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
            return None

        account_data = {}

        # 遍历文件夹中的所有txt文件
        for filename in os.listdir(folder_path):
            if not filename.endswith('.txt'):
                continue

            # 解析文件名格式: "账号---天数#总天数"
            # 例如: "<EMAIL>---3#7.txt" 或 "<EMAIL>---4#7（6）.txt"
            base_name = filename[:-4]  # 去掉.txt后缀

            # 移除重复下载标识，如 (1), (2), （1）, （2） 等
            base_name = re.sub(r'[（(]\d+[）)]$', '', base_name)

            if '---' not in base_name:
                continue

            try:
                account_part, days_part = base_name.split('---', 1)
                account_name = account_part.strip()

                # 解析天数部分，可能是 "2#7" 或 "2" 格式
                if '#' in days_part:
                    current_days = int(days_part.split('#')[0])
                else:
                    current_days = int(days_part)

                # 对同一账号，保留最大天数
                if account_name in account_data:
                    if current_days > account_data[account_name]:
                        account_data[account_name] = current_days
                else:
                    account_data[account_name] = current_days

            except (ValueError, IndexError):
                # 如果解析失败，跳过这个文件
                continue

        return account_data if account_data else None

    except Exception as e:
        return None

def check_if_max_days_reached(account, folder_path):
    """检查账号是否达到最大天数（如7#7这种情况）"""
    try:
        if not os.path.exists(folder_path):
            return False

        for filename in os.listdir(folder_path):
            if not filename.endswith('.txt'):
                continue

            # 移除重复下载标识
            base_name = filename[:-4]
            base_name = re.sub(r'[（(]\d+[）)]$', '', base_name)

            if '---' not in base_name:
                continue

            try:
                account_part, days_part = base_name.split('---', 1)
                if account_part.strip() == account and '#' in days_part:
                    # 检查是否是 x#x 格式（当前天数等于总天数）
                    parts = days_part.split('#')
                    if len(parts) == 2:
                        current_days = int(parts[0])
                        total_days = int(parts[1])
                        if current_days == total_days:
                            return True
            except (ValueError, IndexError):
                continue

        return False
    except Exception:
        return False

def compare_daily_files_table_format():
    """读取并对比今日和昨日的日期文件夹中的txt文件，使用表格格式显示"""
    try:
        # 检查用户的RTBS目录
        user_home = os.path.expanduser("~")
        user_rtbs_dir = os.path.join(user_home, "Downloads", "RTBS")

        # 优先使用用户目录，如果不存在则使用默认配置
        stalk_dir = user_rtbs_dir if os.path.exists(user_rtbs_dir) else STALK_DIR

        if not os.path.exists(stalk_dir):
            return {
                'success': False,
                'message': f'RTBS目录不存在: {stalk_dir}',
                'data': None
            }

        # 获取今日和昨日的日期字符串
        today = datetime.now()
        yesterday = today - timedelta(days=1)

        today_str = f"{today.month}月{today.day}日"
        yesterday_str = f"{yesterday.month}月{yesterday.day}日"

        today_folder = os.path.join(stalk_dir, today_str)
        yesterday_folder = os.path.join(stalk_dir, yesterday_str)

        # 读取今日文件夹中的所有txt文件
        today_data = read_folder_account_data(today_folder)
        if today_data is None:
            return {
                'success': False,
                'message': f'今日文件夹 {today_str} 不存在或为空',
                'data': None
            }

        # 读取昨日文件夹中的所有txt文件
        yesterday_data = read_folder_account_data(yesterday_folder)

        # 获取所有账号（今日和昨日的并集），按今日天数排序
        if yesterday_data is None:
            all_accounts = set(today_data.keys())
        else:
            all_accounts = set(today_data.keys()) | set(yesterday_data.keys())

        # 创建账号列表，按天数排序（天数最大的排在上面）
        account_list = []
        for account in all_accounts:
            today_days = today_data.get(account, 0)
            yesterday_days = yesterday_data.get(account, 0) if yesterday_data else 0
            account_list.append((account, today_days, yesterday_days))

        # 按今日天数降序排序
        account_list.sort(key=lambda x: x[1], reverse=True)

        # 处理每个账号的状态
        accounts = []
        for i, (account, today_days, yesterday_days) in enumerate(account_list, 1):
            # 确定状态和颜色
            if yesterday_data is None or account not in yesterday_data:
                # 新账号
                status = "New"
                status_class = "new"
            elif account not in today_data:
                # 账号消失
                status = "已移除"
                status_class = "removed"
                today_days = 0
            elif today_days == yesterday_days + 1:
                # 正常增加（签到成功）
                status = "签到成功"
                status_class = "success"
            elif today_days == yesterday_days:
                # 天数相同，检查是否是7#7这种情况（通过检查文件名）
                is_max_days = check_if_max_days_reached(account, today_folder)
                if is_max_days:
                    status = "签到结束"
                    status_class = "success"
                else:
                    status = "签到失败"
                    status_class = "failed"
            else:
                # 其他情况
                change = today_days - yesterday_days
                if change > 1:
                    status = f"增加{change}天"
                    status_class = "success"
                else:
                    status = f"减少{abs(change)}天"
                    status_class = "failed"

            accounts.append({
                'index': i,
                'account': account,
                'today_days': today_days,
                'yesterday_days': yesterday_days,
                'status': status,
                'status_class': status_class
            })

        result = {
            'today_date': today_str,
            'yesterday_date': yesterday_str,
            'accounts': accounts,
            'stalk_dir': stalk_dir,
            'total_accounts': len(accounts)
        }

        return {
            'success': True,
            'message': f'读取记录完成，共检查 {len(accounts)} 个账号',
            'data': result
        }

    except Exception as e:
        return {
            'success': False,
            'message': f'读取记录失败: {str(e)}',
            'data': None
        }

def get_chrome_profile_info():
    """获取Chrome配置文件对应的账号信息"""
    try:
        # 这里可以扩展，读取配置文件与账号的对应关系
        # 暂时返回基本信息
        return {
            'success': True,
            'profiles': []
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

if __name__ == "__main__":
    try:
        result = compare_daily_files_table_format()
        print(json.dumps(result, ensure_ascii=False, indent=2))
    except Exception as e:
        error_result = {
            'success': False,
            'message': f'程序执行失败: {str(e)}',
            'data': None
        }
        print(json.dumps(error_result, ensure_ascii=False, indent=2))
