/* Chrome Profile Launcher - 重新设计 */

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
    overflow: hidden;
}

/* 应用主容器 - 垂直布局 */
.app {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

/* 顶部分割线 */
.top-divider {
    height: 1px;
    background: #dee2e6;
    flex-shrink: 0;
}

/* 配置文件区域 - 顶部，占据主要空间 */
.profiles-section {
    background: #fff;
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
}

/* 批量操作区域 - 中间，固定高度 */
.batch-section {
    background: #fff;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    height: auto;
    border-top: 1px solid #dee2e6;
}

/* 日志区域 - 底部，固定高度 */
.log-section {
    background: #fff;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    height: 200px;
    border-top: 1px solid #dee2e6;
}

/* 统一的区域头部 */
.section-header {
    height: 32px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    flex-shrink: 0;
}

.section-title {
    font-size: 13px;
    font-weight: 600;
    color: #495057;
}

.selection-count {
    font-size: 12px;
    color: #6c757d;
}

/* 头部操作按钮 */
.header-actions {
    display: flex;
    gap: 4px;
}

.icon-btn {
    width: 20px;
    height: 20px;
    border: 1px solid #ced4da;
    background: #fff;
    color: #6c757d;
    cursor: pointer;
    border-radius: 0;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: none;
}

.icon-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}

/* 配置文件容器 */
.profiles-container {
    flex: 1;
    padding: 8px;
    overflow: hidden;
    min-height: 0;
}

/* 配置文件网格 - 适应垂直布局 */
.profiles-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 2px;
    height: 100%;
    overflow-y: auto;
    align-content: start;
}

/* 配置文件项 - 完全按照图片样式 */
.profile-item {
    aspect-ratio: 1;
    border: 1px solid #ced4da;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 13px;
    color: #495057;
    border-radius: 0;
    transition: none;
    user-select: none;
    font-weight: 500;
}

.profile-item:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

.profile-item.selected {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.profile-item.placeholder {
    background: transparent;
    border-color: #e9ecef;
    cursor: default;
}

.profile-item.placeholder:hover {
    background: transparent;
    border-color: #e9ecef;
}

/* 批量操作容器 */
.batch-container {
    padding: 8px;
    flex-shrink: 0;
}

/* 按钮行 - 3x3布局 */
.button-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    margin-bottom: 4px;
}

.button-row:last-child {
    margin-bottom: 0;
}

/* 批量操作按钮 - 完全按照图片样式 */
.batch-btn {
    height: 28px;
    border: 1px solid;
    background: #fff;
    cursor: pointer;
    font-size: 11px;
    border-radius: 0;
    transition: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
}

/* 按钮颜色样式 - 完全按照图片 */
.batch-btn.primary {
    border-color: #007bff;
    color: #007bff;
}

.batch-btn.primary:hover {
    background: #e7f3ff;
}

.batch-btn.success {
    border-color: #28a745;
    color: #28a745;
}

.batch-btn.success:hover {
    background: #e8f5e8;
}

.batch-btn.bilibili {
    border-color: #ff69b4;
    color: #ff69b4;
    background: #fff;
}

.batch-btn.bilibili:hover {
    background: #fff5fb;
}

.batch-btn.info {
    border-color: #17a2b8;
    color: #17a2b8;
}

.batch-btn.info:hover {
    background: #e6f7ff;
}

.batch-btn.warning {
    border-color: #ffc107;
    color: #856404;
}

.batch-btn.warning:hover {
    background: #fff8e1;
}

.batch-btn.danger {
    border-color: #dc3545;
    color: #dc3545;
}

.batch-btn.danger:hover {
    background: #ffeaea;
}

.batch-btn.option {
    border-color: #6c757d;
    color: #6c757d;
}

.batch-btn.option:hover {
    background: #f8f9fa;
}

/* 选项按钮选中状态 - 紫色效果 */
.batch-btn.option.selected {
    background: #f3e8ff;
    border-color: #8b5cf6;
    color: #6b46c1;
}

.batch-btn.placeholder {
    border-color: #e9ecef;
    background: #fff;
    cursor: default;
    color: #adb5bd;
}



/* 日志容器 */
.log-container {
    flex: 1;
    overflow: hidden;
}

.log-content {
    height: 100%;
    padding: 8px;
    overflow-y: auto;
    background: #f8f9fa;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
    line-height: 1.3;
    white-space: pre-wrap;
    word-wrap: break-word;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

/* 日志条目样式 */
.log-entry {
    margin-bottom: 2px;
    padding: 1px 0;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
    line-height: 1.3;
    width: 100%;
    text-align: left;
}

.log-entry.info { color: #007bff; }
.log-entry.success { color: #28a745; }
.log-entry.warning { color: #ffc107; }
.log-entry.error { color: #dc3545; }
.log-entry.option { color: #8b5cf6; }



/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    width: 80%;
    height: 80%;
    background: #fff;
    border: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
}

.modal-header {
    height: 40px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
}

.modal-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.modal-body {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-spinner {
    background: #fff;
    padding: 24px;
    border-radius: 4px;
    text-align: center;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: #6c757d;
}
