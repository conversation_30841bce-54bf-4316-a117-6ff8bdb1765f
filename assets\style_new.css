/* Chrome Profile Launcher - 规范化样式 */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
    background: #f5f5f5;
    overflow: hidden;
}

/* 应用容器 */
.app-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    min-height: 0;
}

/* 左侧面板 - 配置文件 */
.left-panel {
    width: 60%;
    background: #fff;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
}

/* 统一标题栏样式 */
.panel-header,
.log-header,
.results-header {
    height: 30px;
    background: #fafafa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    flex-shrink: 0;
}

.panel-title,
.log-title {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    line-height: 1;
}

/* 标题栏控制按钮组 */
.panel-controls,
.log-controls {
    display: flex;
    gap: 3px;
}

/* 统一标题栏按钮样式 */
.control-btn {
    width: 18px;
    height: 18px;
    border: 1px solid #ccc;
    background: #fff;
    color: #666;
    cursor: pointer;
    border-radius: 0;
    font-size: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: none;
}

.control-btn:hover {
    background: #f0f0f0;
    border-color: #999;
    color: #333;
}

.control-btn:active {
    background: #e8e8e8;
}

/* 配置文件列表区域 */
.profile-list {
    flex: 1;
    padding: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 配置文件网格 */
.profile-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 2px;
    flex: 1;
    overflow-y: auto;
    align-content: start;
}

/* 配置文件项 */
.profile-item {
    aspect-ratio: 1;
    border: 1px solid #d0d0d0;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 11px;
    color: #333;
    border-radius: 0;
    transition: none;
    user-select: none;
}

.profile-item:hover {
    background: #f0f0f0;
    border-color: #999;
}

.profile-item.selected {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}

.profile-item.placeholder {
    background: transparent;
    border-color: #e0e0e0;
    cursor: default;
}

.profile-item.placeholder:hover {
    background: transparent;
    border-color: #e0e0e0;
}

/* 右侧面板 */
.right-panel {
    width: 40%;
    background: #fff;
    display: flex;
    flex-direction: column;
}

/* 操作区域 */
.action-area {
    padding: 6px;
    border-bottom: 1px solid #e0e0e0;
    flex-shrink: 0;
}

/* 操作区域标题 - 统一样式 */
.action-section {
    margin-bottom: 4px;
}

.section-title {
    height: 30px;
    background: #fafafa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    font-size: 12px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    line-height: 1;
}

/* 操作按钮网格 */
.action-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    margin-bottom: 4px;
}

/* 操作按钮 */
.action-btn {
    height: 24px;
    border: 1px solid #d0d0d0;
    background: #fff;
    color: #333;
    cursor: pointer;
    font-size: 10px;
    border-radius: 0;
    transition: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: #f5f5f5;
    border-color: #999;
}

/* 按钮颜色变体 */
.action-btn.primary {
    border-color: #2196f3;
    color: #2196f3;
}

.action-btn.success {
    border-color: #4caf50;
    color: #4caf50;
}

.action-btn.warning {
    border-color: #ff9800;
    color: #ff9800;
}

.action-btn.info {
    border-color: #00bcd4;
    color: #00bcd4;
}

.action-btn.cache {
    border-color: #ffc107;
    color: #ffc107;
}

.action-btn.error {
    border-color: #f44336;
    color: #f44336;
}

/* 哔哩搜索按钮特殊样式 - 浅粉色 */
.action-btn.warning {
    border-color: #ffb3d9;
    color: #ff69b4;
    background: #fff5fb;
}

/* 日志面板 */
.log-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

/* 日志头部样式已在统一标题栏中定义 */

.log-content {
    flex: 1;
    padding: 6px;
    overflow-y: auto;
    background: #fafafa;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 10px;
    line-height: 1.2;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 日志条目 */
.log-entry {
    margin-bottom: 2px;
    padding: 2px 4px;
    border-radius: 0;
}

.log-entry.info {
    color: #2196f3;
}

.log-entry.success {
    color: #4caf50;
}

.log-entry.warning {
    color: #ff9800;
}

.log-entry.error {
    color: #f44336;
}

/* 结果面板 */
.results-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 80%;
    background: #fff;
    border: 1px solid #e0e0e0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    flex-direction: column;
    z-index: 1000;
}

/* 结果面板头部 - 弹窗样式 */
.results-header {
    padding: 0 12px; /* 稍大的内边距 */
}

.results-header h3 {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    margin: 0;
    line-height: 1;
}

.results-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-content {
    background: #fff;
    padding: 24px;
    border-radius: 4px;
    text-align: center;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2196f3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: #666;
}
