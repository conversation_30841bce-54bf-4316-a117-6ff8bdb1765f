# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Electron
dist/
out/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Test files
test_*.py
test_*.js
test_*.html
*_test.py
*_test.js
*_test.html
test/
tests/

# Debug files
debug_*.py
debug_*.js
debug_*.html
*_debug.py
*_debug.js
*_debug.html

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Documentation drafts
*_DRAFT.md
DRAFT_*.md
